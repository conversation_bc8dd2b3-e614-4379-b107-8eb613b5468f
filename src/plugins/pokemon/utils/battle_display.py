"""
Battle Display Utilities

Enhanced display formatting for Pokemon battles.
"""

from typing import Dict, List, Any, Optional
from ..models import BattleStatus, Battle, BattlePlayer
from ..translation import TranslationLayer


class BattleDisplayFormatter:
    """Formats battle information for user display"""
    
    def __init__(self, translation_layer: Optional[TranslationLayer] = None):
        self.translation = translation_layer
        
    def format_battle_overview(self, summary: Dict[str, Any], show_actions: bool = True) -> str:
        """Format a comprehensive battle overview"""
        lines = []
        
        # Header
        battle_id = summary['id']
        short_id = battle_id[-6:] if len(battle_id) > 6 else battle_id
        lines.append(f"🎮 对战概览 (ID: {short_id})")
        lines.append("=" * 30)
        
        # Basic info
        status = self._translate_status(summary['status'])
        format_name = self._translate_format(summary['format'])
        lines.append(f"📋 格式: {format_name}")
        lines.append(f"⚡ 状态: {status}")
        lines.append(f"🔄 回合: {summary['current_turn']}")
        lines.append("")
        
        # Players
        lines.append("👥 玩家:")
        for player_id, player_info in summary['players'].items():
            ready_status = "✅" if player_info.get('ready', False) else "⏳"
            lines.append(f"  {player_id}: {player_info['username']} {ready_status}")
        lines.append("")
        
        # Winner if finished
        if summary.get('winner'):
            lines.append(f"🏆 获胜者: {summary['winner']}")
            lines.append("")
            
        # Team preview specific info
        if summary['status'].lower() == 'team_preview' and 'team_preview' in summary:
            team_preview = summary['team_preview']
            lines.append("👀 队伍预览:")

            for player_id, team in team_preview.get('teams', {}).items():
                lines.append(f"  {player_id} 的队伍:")
                for i, pokemon in enumerate(team, 1):
                    name = self._translate_pokemon_name(pokemon.get('species', 'Unknown'))
                    level = pokemon.get('level', 50)
                    item = pokemon.get('item')
                    item_text = f" @{item}" if item else ""
                    lines.append(f"    {i}. Lv.{level} {name}{item_text}")
                lines.append("")

            ready_players = team_preview.get('ready_players', [])
            if ready_players:
                lines.append(f"✅ 已准备: {', '.join(ready_players)}")
                lines.append("")

        # Action guidance based on status
        if show_actions:
            action_guide = self._get_action_guidance(summary)
            if action_guide:
                lines.append("💡 可用操作:")
                lines.extend(action_guide)

        return "\n".join(lines)
        
    def format_team_preview_guide(self, battle_id: str, player_teams: Dict[str, List[Dict]]) -> str:
        """Format team preview guidance"""
        lines = []
        short_id = battle_id[-6:] if len(battle_id) > 6 else battle_id
        
        lines.append(f"👀 队伍预览 (ID: {short_id})")
        lines.append("=" * 30)
        lines.append("")
        
        # Show teams
        for player_id, team in player_teams.items():
            lines.append(f"🔸 {player_id} 的队伍:")
            for i, pokemon in enumerate(team, 1):
                name = self._translate_pokemon_name(pokemon.get('species', 'Unknown'))
                lines.append(f"  {i}. {name}")
            lines.append("")
            
        # Instructions
        lines.append("📝 操作说明:")
        lines.append("• 选择首发宝可梦: /行动 team 1-6")
        lines.append("• 查看详细状态: /状态")
        lines.append("• 投降: /投降")
        lines.append("")
        lines.append("⚠️  请选择你的首发宝可梦编号 (1-6)")
        
        return "\n".join(lines)
        
    def format_battle_field(self, battle_data: Dict[str, Any]) -> str:
        """Format current battle field state"""
        lines = []
        
        lines.append("⚔️ 战场状态")
        lines.append("=" * 20)
        
        # Active Pokemon
        if 'active_pokemon' in battle_data:
            for player_id, pokemon in battle_data['active_pokemon'].items():
                name = self._translate_pokemon_name(pokemon.get('species', 'Unknown'))
                hp_percent = pokemon.get('hp_percent', 100)
                status = pokemon.get('status', '')
                
                hp_bar = self._create_hp_bar(hp_percent)
                status_text = f" [{status}]" if status else ""
                
                lines.append(f"{player_id}: {name} {hp_bar}{status_text}")
                
        lines.append("")
        
        # Available actions
        if 'available_actions' in battle_data:
            lines.append("🎯 可用行动:")
            actions = battle_data['available_actions']
            
            if 'moves' in actions:
                lines.append("  技能:")
                for i, move in enumerate(actions['moves'], 1):
                    move_name = self._translate_move_name(move.get('name', 'Unknown'))
                    pp = move.get('pp', 0)
                    max_pp = move.get('max_pp', 0)
                    disabled = " (禁用)" if move.get('disabled') else ""
                    lines.append(f"    {i}. {move_name} ({pp}/{max_pp}){disabled}")
                    
            if 'switches' in actions:
                lines.append("  替换:")
                for i, pokemon in enumerate(actions['switches'], 1):
                    name = self._translate_pokemon_name(pokemon.get('species', 'Unknown'))
                    hp_percent = pokemon.get('hp_percent', 100)
                    fainted = " (濒死)" if hp_percent <= 0 else ""
                    lines.append(f"    {i}. {name} ({hp_percent}%){fainted}")
                    
        return "\n".join(lines)
        
    def format_simple_commands_help(self) -> str:
        """Format help for simplified commands"""
        lines = [
            "🎮 简化命令帮助",
            "=" * 20,
            "",
            "⚔️ 战斗命令:",
            "• /挑战 <用户> [格式] - 发起挑战",
            "• /接受 - 接受挑战",
            "• /状态 - 查看当前战斗",
            "• /投降 - 投降当前战斗",
            "",
            "🎯 战斗行动:",
            "• /行动 move 1-4 - 使用技能",
            "• /行动 switch 1-6 - 替换宝可梦",
            "• /行动 team 1-6 - 选择首发 (队伍预览)",
            "",
            "💡 提示:",
            "• 系统会自动记住你当前的战斗",
            "• 不需要每次输入完整的战斗ID",
            "• 使用 /状态 查看详细信息和可用操作"
        ]
        return "\n".join(lines)
        
    def _translate_status(self, status: str) -> str:
        """Translate battle status"""
        status_map = {
            'waiting': '等待中',
            'team_preview': '队伍预览',
            'active': '进行中',
            'finished': '已结束',
            'cancelled': '已取消'
        }
        return status_map.get(status.lower(), status)
        
    def _translate_format(self, format_name: str) -> str:
        """Translate format name"""
        if self.translation:
            return self.translation.translate_format(format_name)
        return format_name
        
    def _translate_pokemon_name(self, name: str) -> str:
        """Translate Pokemon name"""
        if self.translation:
            return self.translation.translate_pokemon_name(name)
        return name
        
    def _translate_move_name(self, name: str) -> str:
        """Translate move name"""
        if self.translation:
            return self.translation.translate_move_name(name)
        return name
        
    def _create_hp_bar(self, hp_percent: float) -> str:
        """Create HP bar visualization"""
        if hp_percent > 75:
            color = "🟢"
        elif hp_percent > 50:
            color = "🟡"
        elif hp_percent > 25:
            color = "🟠"
        else:
            color = "🔴"
            
        bar_length = 10
        filled = int(hp_percent / 100 * bar_length)
        empty = bar_length - filled
        
        bar = "█" * filled + "░" * empty
        return f"{color} {bar} {hp_percent:.0f}%"
        
    def _get_action_guidance(self, summary: Dict[str, Any]) -> List[str]:
        """Get action guidance based on battle status"""
        status = summary['status'].lower()
        
        if status == 'team_preview':
            return [
                "  • 选择首发: /行动 team <编号>",
                "  • 查看队伍: /状态"
            ]
        elif status == 'active':
            return [
                "  • 使用技能: /行动 move <编号>",
                "  • 替换宝可梦: /行动 switch <编号>",
                "  • 查看详情: /状态"
            ]
        elif status == 'waiting':
            return [
                "  • 等待对手加入战斗"
            ]
        else:
            return []
